#!/usr/bin/env python3
"""
Gemini-Google Sheets Integration
Enables natural language queries against Google Sheets data using Gemini AI
"""

import os
import json
import asyncio
from typing import Any, Dict, List, Optional
from google.auth import default
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from google import genai

class GeminiSheetsIntegration:
    """Integration class for Gemini AI and Google Sheets"""

    def __init__(self, project_id: Optional[str] = None, location: Optional[str] = None):
        """Initialize the integration with Google Sheets and Gemini AI"""
        self.sheets_service = None
        self.gemini_client = None
        self.project_id = project_id
        self.location = location
        self.setup_google_sheets()
        self.setup_gemini()

    def setup_google_sheets(self):
        """Set up Google Sheets API client using Application Default Credentials"""
        try:
            credentials, project = default(scopes=[
                'https://www.googleapis.com/auth/spreadsheets.readonly',
                'https://www.googleapis.com/auth/drive.readonly'
            ])

            self.sheets_service = build('sheets', 'v4', credentials=credentials)
            print(f"✅ Google Sheets API initialized for project: {project}")

        except Exception as error:
            print(f"❌ Error setting up Google Sheets API: {error}")
            raise

    def setup_gemini(self):
        """Set up Gemini AI client using Vertex AI"""
        try:
            # Get project ID and location from parameters or environment
            if not self.project_id:
                self.project_id = os.getenv('GOOGLE_CLOUD_PROJECT') or os.getenv('GCLOUD_PROJECT')

            if not self.location:
                self.location = os.getenv('GOOGLE_CLOUD_LOCATION', 'us-central1')

            if not self.project_id:
                self.project_id = "searce-playground-v2"
                print(f"⚠️  Google Cloud Project ID not found, using default: {self.project_id}")

            if not self.project_id:
                raise ValueError("Google Cloud Project ID is required for Vertex AI")

            # Initialize Vertex AI client
            self.gemini_client = genai.Client(
                vertexai=True,
                project=self.project_id,
                location=self.location
            )

            print(f"✅ Gemini AI (Vertex AI) initialized successfully")
            print(f"📋 Project: {self.project_id}")
            print(f"📋 Location: {self.location}")

        except Exception as error:
            print(f"❌ Error setting up Gemini AI: {error}")
            raise

    def get_spreadsheet_data(self, spreadsheet_id: str, sheet_name: str = None, range_name: str = None) -> Dict[str, Any]:
        """Fetch data from a Google Spreadsheet"""
        try:
            # Get spreadsheet metadata
            spreadsheet = self.sheets_service.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()
            title = spreadsheet.get('properties', {}).get('title', 'Unknown')

            # If no sheet specified, use the first one
            if not sheet_name:
                sheets = spreadsheet.get('sheets', [])
                if sheets:
                    sheet_name = sheets[0].get('properties', {}).get('title', 'Sheet1')
                else:
                    raise ValueError("No sheets found in spreadsheet")

            # Build range string
            if range_name:
                full_range = f"{sheet_name}!{range_name}"
            else:
                full_range = sheet_name

            # Get the data
            result = self.sheets_service.spreadsheets().values().get(
                spreadsheetId=spreadsheet_id,
                range=full_range
            ).execute()

            values = result.get('values', [])

            return {
                'spreadsheet_title': title,
                'sheet_name': sheet_name,
                'range': full_range,
                'data': values,
                'row_count': len(values),
                'column_count': len(values[0]) if values else 0
            }

        except HttpError as error:
            print(f"❌ HTTP Error accessing spreadsheet: {error}")
            raise
        except Exception as error:
            print(f"❌ Error fetching spreadsheet data: {error}")
            raise

def format_data_for_analysis(data: Dict[str, Any]) -> str:
    """Format spreadsheet data for Gemini analysis"""
    lines = [
        f"Spreadsheet: {data['spreadsheet_title']}",
        f"Sheet: {data['sheet_name']}",
        f"Data ({data['row_count']} rows, {data['column_count']} columns):",
        ""
    ]

    # Add the actual data
    for i, row in enumerate(data['data']):
        # Convert row to string, handling different data types
        row_str = [str(cell) if cell is not None else '' for cell in row]
        lines.append(f"Row {i+1}: {', '.join(row_str)}")

    return '\n'.join(lines)

def query_data_with_gemini(client, data_text: str) -> str:
    """Query the spreadsheet data using Gemini AI via Vertex AI"""
    prompt = f"""
     You are a Google Cloud migration and infrastructure expert. Analyze the provided GCP infrastructure inventory:
      {data_text} and generate a **detailed summary sheet** suitable for appending to an Excel workbook.

## 🎯 Goal
Produce a comprehensive, Excel-compatible summary of the current infrastructure across all GCP services. The summary should:
- Reflect total counts and usage patterns
- Identify configurations that follow or violate GCP best practices
- Highlight potential risks, inefficiencies, and modernization blockers
- Include recommendations **only if improvement is needed**
- Be exhaustive across services, not limited to issues only

## ✅ Output Format (Excel Summary Sheet)
Return each row in the following format:

**Resource Type | Category | Description | Current State or Count | Assessment | Recommendation (if needed) | Priority (High/Medium/Low)**

- Use pipe delimiter (`|`) for Excel parsing
- Do **not** use bullet points or markdown
- Each row = one insight, observation, or gap
- Be quantitative and technically specific
- **Include recommendations only if something violates or lacks a best practice**
- Leave the recommendation field blank when the current setup is aligned

## ✅ Sample Output Rows

Compute Engine | Inventory | Total number of VMs | 320 | Current VM count across 14 projects |  | Low  
Compute Engine | Security | VMs with public IPs | 30 | External exposure risk | Move to private IPs with IAP | High  
Compute Engine | Best Practice | Shielded VMs enabled | 290 (91%) | Follows GCP security baseline |  | Low  
IAM | Governance | IAM roles assigned to individual users | 75 users | Not group-based; hard to manage | Migrate to group-based IAM via Cloud Identity | Medium  
Cloud SQL | Networking | Instances using public IPs | 16 | Not aligned with private access standards | Use VPC peering and private IPs | High  
Cloud Storage | Optimization | Buckets missing lifecycle policy | 24 buckets | Cost risk for long-term data | Add lifecycle rules for cold storage transition | Medium  
Firewall | Security | Ingress rules with 0.0.0.0/0 | 9 rules | Broad access increases attack surface | Restrict CIDR ranges in ingress policies | High  
GKE | Inventory | GKE clusters deployed | 12 | Mix of public and private clusters |  | Medium  
Cloud Run | IAM | Services using default service account | 22 | Excessive permissions risk | Assign custom least-privilege service accounts | High  
Labels | Governance | Resources without labels | 90% unlabeled | Poor tracking for cost and ownership | Enforce labeling via org policies | Medium  
Logging | Monitoring | Org-level aggregated sink configured | No | Incomplete logging visibility | Setup centralized logging to BigQuery | High  
Quotas | Resource Health | Resources at >80% quota usage | 13 quotas | Risk of resource exhaustion | Enable quota alerting and optimize usage | High  
Subnets | Networking | Subnets without Private Google Access | 52 | Unable to access APIs without external IPs | Enable Private Google Access | Medium  
NAT | Inventory | NAT gateways in use | 19 | Cost and management overhead | Consolidate NAT configs where possible | Low  
Unknown | Clarification | OS types missing for VMs | N/A | Required for modernization mapping | Provide OS type and version for each VM | High

## 📁 Coverage Requirements
Include rows for all relevant services and layers, include them only if they are present in the inventory sheet:

- **Compute (VMs, MIGs, images, Shielded VMs)**
- **Serverless (Cloud Run, App Engine)**
- **Containers (GKE clusters, workloads, endpoint types)**
- **Networking (VPCs, subnets, firewall rules, load balancers, NATs, DNS, interconnects)**
- **IAM & Security (service accounts, role bindings, org policies, encryption, IAP)**
- **Storage (Cloud Storage, Persistent Disks, lifecycle policies, snapshot sprawl)**
- **Databases (Cloud SQL, Firestore, Memorystore, Spanner)**
- **Logging & Monitoring (Cloud Logging, alerting, metrics, log sinks)**
- **Quotas & Limits (resource usage, exhaustion risks)**
- **Governance (labels, billing accounts, folder hierarchy, org-level settings)**
- **Clarifications (if inventory is missing key input fields that block recommendations)**

## 🧠 Guidelines
- Be clear and technically accurate.
- Include services even if no misconfigurations are found, and include them only if they are present in inventory sheet.
- Do not propose architecture changes — only assess what's present.
- ONLY INCLUDE INFORMATION THAT IS PRESENT IN THE INVENTORY SHEET
- Leave recommendation blank when current configuration follows GCP best practices.
- Use **High** priority for security, exposure, or availability risks.
- Use **Medium** for optimization or improvement needs.
- Use **Low** when setup is healthy or informational only.
- Mention questions if needed to be asked to the client pertaining to any specific resource to clarify on a situation, ONLY IF NEEDED/

The output must be structured, tabular, Excel-friendly, and immediately usable as a summary sheet for migration planning.
"""

    try:
        response = client.models.generate_content(
            model="gemini-2.5-pro",
            contents=prompt
        )
        return response.text
    except Exception as error:
        raise Exception(f"Gemini API error: {error}")

def main():
    """Main function to demonstrate the integration"""
    print("🚀 Gemini-Google Sheets Integration")
    print("=" * 50)

    # Test spreadsheet ID (our created test sheet)
    SPREADSHEET_ID = "1rjpEvkpAXipraHCGk2U895IanIwo9Kb3051eKzkTtHo"
    OUTPUT_FILENAME = "response.txt"

    try:
        # Initialize the integration
        integration = GeminiSheetsIntegration()

        # Fetch spreadsheet data
        print(f"\n📊 Fetching data from spreadsheet...")
        data = integration.get_spreadsheet_data(SPREADSHEET_ID)

        print(f"✅ Successfully loaded: {data['spreadsheet_title']}")
        print(f"📋 Sheet: {data['sheet_name']}")
        print(f"📏 Dimensions: {data['row_count']} rows × {data['column_count']} columns")

        # Prepare data for Gemini analysis
        data_text = format_data_for_analysis(data)

        # Define a single prompt
        # prompt = "Summarize the key findings from the data."
        print(f"\n🤖 Running query with Gemini AI...")
    

        # Get the response from Gemini
        response = query_data_with_gemini(integration.gemini_client, data_text)
        print(f"🤖 Gemini Response Received.")

        # Write the response to a file
        with open(OUTPUT_FILENAME, "w") as f:
            f.write(response)
        print(f"✅ Response successfully written to {OUTPUT_FILENAME}")

        print("\n👋 Task complete!")

    except Exception as error:
        print(f"❌ Error in main: {error}")


if __name__ == "__main__":
    main()

