"""
Tests for data processing service.
"""

import pytest
from unittest.mock import patch, mock_open

from gemini_sheets_integration.config import Settings
from gemini_sheets_integration.services.data_processor import DataProcessor
from gemini_sheets_integration.exceptions import DataProcessingError


class TestDataProcessor:
    """Test cases for DataProcessor class."""
    
    @pytest.fixture
    def settings(self):
        """Create test settings."""
        return Settings()
    
    @pytest.fixture
    def processor(self, settings):
        """Create DataProcessor instance."""
        return DataProcessor(settings)
    
    @pytest.fixture
    def sample_spreadsheet_data(self):
        """Sample spreadsheet data for testing."""
        return {
            'spreadsheet_title': 'Test Spreadsheet',
            'ranges': ['Sheet1'],
            'data': {
                'Sheet1': [
                    ['Header1', 'Header2', 'Header3'],
                    ['Value1', 'Value2', 'Value3'],
                    ['Value4', 'Value5', 'Value6']
                ]
            },
            'total_rows': 3,
            'sheet_count': 1
        }
    
    def test_format_spreadsheet_data(self, processor, sample_spreadsheet_data):
        """Test formatting spreadsheet data."""
        result = processor.format_spreadsheet_data(sample_spreadsheet_data)
        
        assert "Spreadsheet: Test Spreadsheet" in result
        assert "Total Rows: 3" in result
        assert "Sheet Count: 1" in result
        assert "Data from Sheet1:" in result
        assert "Row 1: Header1, Header2, Header3" in result
        assert "Row 2: Value1, Value2, Value3" in result
    
    def test_format_spreadsheet_data_with_none_values(self, processor):
        """Test formatting data with None values."""
        data = {
            'spreadsheet_title': 'Test',
            'ranges': ['Sheet1'],
            'data': {
                'Sheet1': [
                    ['Header1', None, 'Header3'],
                    ['Value1', '', None]
                ]
            },
            'total_rows': 2,
            'sheet_count': 1
        }
        
        result = processor.format_spreadsheet_data(data)
        assert "Row 1: Header1, , Header3" in result
        assert "Row 2: Value1, , " in result
    
    def test_format_spreadsheet_data_error(self, processor):
        """Test error handling in format_spreadsheet_data."""
        with pytest.raises(DataProcessingError):
            processor.format_spreadsheet_data(None)
    
    def test_parse_pipe_delimited_response(self, processor):
        """Test parsing pipe-delimited response."""
        response_text = """Compute Engine | Security | VMs with public IPs | 30 | High risk | Use private IPs | High
IAM | Governance | Direct user roles | 75 users | Poor practice | Use groups | Medium"""

        result = processor.parse_pipe_delimited_response(response_text)

        # Should have headers plus 2 data rows
        assert len(result) == 3
        
        # Check headers
        expected_headers = [
            "Resource Type", "Category", "Description", 
            "Current State or Count", "Assessment", 
            "Recommendation (if needed)", "Priority"
        ]
        assert result[0] == expected_headers
        
        # Check first data row
        assert result[1][0] == "Compute Engine"
        assert result[1][1] == "Security"
        assert result[1][6] == "High"
    
    def test_parse_pipe_delimited_response_padding(self, processor):
        """Test parsing with rows that need padding."""
        response_text = "Short | Row"
        
        result = processor.parse_pipe_delimited_response(response_text)
        
        # Should have headers plus 1 data row
        assert len(result) == 2
        
        # Data row should be padded to 7 columns
        assert len(result[1]) == 7
        assert result[1][0] == "Short"
        assert result[1][1] == "Row"
        assert result[1][2] == ""  # Padded empty string
    
    def test_parse_pipe_delimited_response_error(self, processor):
        """Test error handling in parse_pipe_delimited_response."""
        with pytest.raises(DataProcessingError):
            processor.parse_pipe_delimited_response(None)
    
    @patch("builtins.open", new_callable=mock_open)
    @patch("pathlib.Path.mkdir")
    def test_save_response_to_file(self, mock_mkdir, mock_file, processor):
        """Test saving response to file."""
        response_text = "Test response content"
        
        result = processor.save_response_to_file(response_text)
        
        assert result is True
        mock_file.assert_called_once()
        mock_file().write.assert_called_once_with(response_text)
    
    def test_save_response_to_file_disabled(self, settings):
        """Test saving when disabled in settings."""
        settings.save_response_to_file = False
        processor = DataProcessor(settings)
        
        result = processor.save_response_to_file("test")
        assert result is False
    
    @patch("builtins.open", side_effect=IOError("File error"))
    def test_save_response_to_file_error(self, mock_file, processor):
        """Test error handling in save_response_to_file."""
        with pytest.raises(DataProcessingError):
            processor.save_response_to_file("test")
    
    def test_validate_spreadsheet_id_valid(self, processor):
        """Test valid spreadsheet ID validation."""
        valid_id = "1WrHxL8RwJFmpZ6KOSkNANqUX8CcOhbkYOJxp2sy3vlA"
        result = processor.validate_spreadsheet_id(valid_id)
        assert result is True
    
    def test_validate_spreadsheet_id_empty(self, processor):
        """Test empty spreadsheet ID validation."""
        with pytest.raises(DataProcessingError, match="cannot be empty"):
            processor.validate_spreadsheet_id("")
    
    def test_validate_spreadsheet_id_too_short(self, processor):
        """Test too short spreadsheet ID validation."""
        with pytest.raises(DataProcessingError, match="too short"):
            processor.validate_spreadsheet_id("short")
    
    def test_validate_spreadsheet_id_invalid_chars(self, processor):
        """Test invalid characters in spreadsheet ID."""
        with pytest.raises(DataProcessingError, match="invalid characters"):
            processor.validate_spreadsheet_id("invalid@#$%^&*()spreadsheet_id")
