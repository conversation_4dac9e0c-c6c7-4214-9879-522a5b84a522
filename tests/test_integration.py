"""
Tests for main integration class.
"""

import pytest
from unittest.mock import Mock, patch

from gemini_sheets_integration.config import Settings
from gemini_sheets_integration.integration import GeminiSheetsIntegration
from gemini_sheets_integration.exceptions import GeminiSheetsError, ValidationError


class TestGeminiSheetsIntegration:
    """Test cases for GeminiSheetsIntegration class."""
    
    @pytest.fixture
    def settings(self):
        """Create test settings."""
        return Settings()
    
    @pytest.fixture
    def mock_services(self):
        """Create mock services."""
        with patch('gemini_sheets_integration.integration.GoogleSheetsService') as mock_sheets, \
             patch('gemini_sheets_integration.integration.GeminiService') as mock_gemini, \
             patch('gemini_sheets_integration.integration.DataProcessor') as mock_processor:
            
            yield {
                'sheets': mock_sheets.return_value,
                'gemini': mock_gemini.return_value,
                'processor': mock_processor.return_value
            }
    
    @pytest.fixture
    def integration(self, settings, mock_services):
        """Create integration instance with mocked services."""
        return GeminiSheetsIntegration(settings)
    
    @pytest.fixture
    def sample_sheet_data(self):
        """Sample sheet data for testing."""
        return {
            'spreadsheet_title': 'Test Spreadsheet',
            'ranges': ['Sheet1'],
            'data': {'Sheet1': [['Header1', 'Header2'], ['Value1', 'Value2']]},
            'total_rows': 2,
            'sheet_count': 1
        }
    
    def test_initialization(self, settings, mock_services):
        """Test integration initialization."""
        integration = GeminiSheetsIntegration(settings)
        
        assert integration.settings == settings
        assert integration.sheets_service is not None
        assert integration.gemini_service is not None
        assert integration.data_processor is not None
    
    def test_initialization_with_default_settings(self, mock_services):
        """Test initialization with default settings."""
        integration = GeminiSheetsIntegration()
        
        assert integration.settings is not None
        assert isinstance(integration.settings, Settings)
    
    def test_run_gcp_security_analysis_success(self, integration, mock_services, sample_sheet_data):
        """Test successful GCP security analysis."""
        # Setup mocks
        mock_services['processor'].validate_spreadsheet_id.return_value = True
        mock_services['sheets'].read_spreadsheet.return_value = sample_sheet_data
        mock_services['processor'].format_spreadsheet_data.return_value = "formatted data"
        mock_services['gemini'].generate_content.return_value = "analysis result"
        mock_services['processor'].parse_pipe_delimited_response.return_value = [["header"], ["data"]]
        mock_services['sheets'].create_sheet.return_value = True
        mock_services['sheets'].write_data.return_value = True
        
        # Run analysis
        result = integration.run_gcp_security_analysis("test_spreadsheet_id")
        
        # Verify result
        assert result is True
        
        # Verify method calls
        mock_services['processor'].validate_spreadsheet_id.assert_called_once()
        mock_services['sheets'].read_spreadsheet.assert_called_once()
        mock_services['processor'].format_spreadsheet_data.assert_called_once()
        mock_services['gemini'].generate_content.assert_called_once()
        mock_services['sheets'].create_sheet.assert_called_once()
        mock_services['sheets'].write_data.assert_called_once()
        mock_services['sheets'].format_headers.assert_called_once()
    
    def test_run_gcp_security_analysis_with_custom_params(self, integration, mock_services, sample_sheet_data):
        """Test GCP security analysis with custom parameters."""
        # Setup mocks
        mock_services['processor'].validate_spreadsheet_id.return_value = True
        mock_services['sheets'].read_spreadsheet.return_value = sample_sheet_data
        mock_services['processor'].format_spreadsheet_data.return_value = "formatted data"
        mock_services['gemini'].generate_content.return_value = "analysis result"
        mock_services['processor'].parse_pipe_delimited_response.return_value = [["header"], ["data"]]
        mock_services['sheets'].create_sheet.return_value = True
        mock_services['sheets'].write_data.return_value = True
        
        # Run analysis with custom parameters
        result = integration.run_gcp_security_analysis(
            spreadsheet_id="test_id",
            sheet_name="CustomSheet",
            output_sheet_name="CustomOutput"
        )
        
        assert result is True
        
        # Verify sheets service was called with custom sheet name
        mock_services['sheets'].read_spreadsheet.assert_called_with(
            spreadsheet_id="test_id",
            ranges=["CustomSheet"]
        )
        
        # Verify output sheet creation
        mock_services['sheets'].create_sheet.assert_called_with("test_id", "CustomOutput")
    
    def test_run_gcp_security_analysis_validation_error(self, integration, mock_services):
        """Test GCP security analysis with validation error."""
        # Setup mock to raise validation error
        mock_services['processor'].validate_spreadsheet_id.side_effect = ValidationError("Invalid ID")
        
        # Run analysis and expect error
        with pytest.raises(GeminiSheetsError):
            integration.run_gcp_security_analysis("invalid_id")
    
    def test_run_custom_analysis_success(self, integration, mock_services, sample_sheet_data):
        """Test successful custom analysis."""
        # Setup mocks
        mock_services['processor'].validate_spreadsheet_id.return_value = True
        mock_services['sheets'].read_spreadsheet.return_value = sample_sheet_data
        mock_services['processor'].format_spreadsheet_data.return_value = "formatted data"
        mock_services['gemini'].generate_content.return_value = "analysis result"
        mock_services['processor'].parse_pipe_delimited_response.return_value = [["header"], ["data"]]
        mock_services['sheets'].create_sheet.return_value = True
        mock_services['sheets'].write_data.return_value = True
        
        # Run custom analysis
        result = integration.run_custom_analysis(
            spreadsheet_id="test_id",
            analysis_type="financial"
        )
        
        assert result is True
        
        # Verify output sheet name
        mock_services['sheets'].create_sheet.assert_called_with("test_id", "Financial_Analysis")
    
    def test_run_custom_analysis_invalid_type(self, integration, mock_services):
        """Test custom analysis with invalid analysis type."""
        mock_services['processor'].validate_spreadsheet_id.return_value = True
        
        with pytest.raises(GeminiSheetsError):
            integration.run_custom_analysis(
                spreadsheet_id="test_id",
                analysis_type="invalid_type"
            )
    
    def test_get_spreadsheet_info_success(self, integration, mock_services, sample_sheet_data):
        """Test successful spreadsheet info retrieval."""
        # Setup mocks
        mock_services['processor'].validate_spreadsheet_id.return_value = True
        mock_services['sheets'].read_spreadsheet.return_value = sample_sheet_data
        
        # Get info
        result = integration.get_spreadsheet_info("test_id")
        
        assert result == sample_sheet_data
        mock_services['processor'].validate_spreadsheet_id.assert_called_once_with("test_id")
        mock_services['sheets'].read_spreadsheet.assert_called_once_with("test_id")
    
    def test_get_spreadsheet_info_error(self, integration, mock_services):
        """Test spreadsheet info retrieval with error."""
        # Setup mock to raise error
        mock_services['processor'].validate_spreadsheet_id.side_effect = Exception("Test error")
        
        with pytest.raises(GeminiSheetsError):
            integration.get_spreadsheet_info("test_id")
