"""
Tests for configuration management.
"""

import pytest
from pydantic import ValidationError

from gemini_sheets_integration.config import Settings, get_settings


class TestSettings:
    """Test cases for Settings class."""
    
    def test_default_settings(self):
        """Test that default settings are valid."""
        settings = Settings()
        
        assert settings.google_cloud_project == "searce-playground-v2"
        assert settings.google_cloud_location == "us-east5"
        assert settings.log_level == "INFO"
        assert settings.gemini_model == "gemini-2.5-pro"
        assert settings.gemini_max_retries == 3
        assert settings.save_response_to_file is True
    
    def test_custom_settings(self):
        """Test settings with custom values."""
        settings = Settings(
            google_cloud_project="test-project",
            google_cloud_location="us-west1",
            log_level="DEBUG",
            gemini_max_retries=5
        )
        
        assert settings.google_cloud_project == "test-project"
        assert settings.google_cloud_location == "us-west1"
        assert settings.log_level == "DEBUG"
        assert settings.gemini_max_retries == 5
    
    def test_log_level_validation(self):
        """Test log level validation."""
        # Valid log levels should work
        for level in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
            settings = Settings(log_level=level)
            assert settings.log_level == level
        
        # Invalid log level should raise error
        with pytest.raises(ValidationError):
            Settings(log_level="INVALID")
    
    def test_project_id_validation(self):
        """Test project ID validation."""
        # Valid project ID should work
        settings = Settings(google_cloud_project="valid-project-123")
        assert settings.google_cloud_project == "valid-project-123"
        
        # Too short project ID should raise error
        with pytest.raises(ValidationError):
            Settings(google_cloud_project="short")
        
        # Empty project ID should raise error
        with pytest.raises(ValidationError):
            Settings(google_cloud_project="")
    
    def test_get_settings_function(self):
        """Test the get_settings function."""
        settings = get_settings()
        assert isinstance(settings, Settings)
        assert settings.google_cloud_project is not None
