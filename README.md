# Gemini Sheets Integration

A production-ready integration between Google Gemini AI and Google Sheets for automated GCP security analysis and reporting.

## 🚀 Features

- **Production-Ready Architecture**: Modular design with proper error handling, logging, and configuration management
- **Multiple Analysis Types**: GCP security analysis, financial analysis, operational analysis, and custom analysis
- **CLI Interface**: User-friendly command-line interface with rich output formatting
- **Comprehensive Logging**: Structured logging with configurable levels and file output
- **Configuration Management**: Environment-based configuration with validation
- **Unit Tests**: Comprehensive test suite with pytest
- **Type Safety**: Full type hints and validation with Pydantic
- **Error Handling**: Custom exceptions and robust error recovery

## 📋 Prerequisites

- Python 3.10 or higher
- Google Cloud Project with the following APIs enabled:
  - Google Sheets API
  - Vertex AI API
- Google Cloud credentials configured (Application Default Credentials)

## 🛠️ Installation

1. **Clone the repository:**
```bash
git clone <repository-url>
cd gemini-with-sheets
```

2. **Create and activate virtual environment:**
```bash
uv venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
```

3. **Install dependencies:**
```bash
uv sync
```

4. **Set up Google Cloud credentials:**
```bash
gcloud auth application-default login
```

5. **Configure environment variables:**
```bash
cp .env.example .env
# Edit .env with your configuration
```

## 🎯 Quick Start

### Using the CLI

**Run GCP Security Analysis:**
```bash
gemini-sheets gcp-security YOUR_SPREADSHEET_ID
```

**Run Custom Analysis:**
```bash
gemini-sheets analyze YOUR_SPREADSHEET_ID --type financial
```

**Get Spreadsheet Information:**
```bash
gemini-sheets info YOUR_SPREADSHEET_ID
```

### Using the Python API

```python
from gemini_sheets_integration import GeminiSheetsIntegration
from gemini_sheets_integration.config import get_settings

# Initialize with default settings
integration = GeminiSheetsIntegration()

# Run GCP security analysis
success = integration.run_gcp_security_analysis(
    spreadsheet_id="your-spreadsheet-id",
    output_sheet_name="Security_Analysis"
)

# Run custom analysis
success = integration.run_custom_analysis(
    spreadsheet_id="your-spreadsheet-id",
    analysis_type="financial",
    output_sheet_name="Financial_Analysis"
)
```

### Using the Main Script

```bash
python main.py
```

## ⚙️ Configuration

The application uses environment variables for configuration. Copy `.env.example` to `.env` and customize:

```bash
# Google Cloud Configuration
GEMINI_SHEETS_GOOGLE_CLOUD_PROJECT=your-gcp-project-id
GEMINI_SHEETS_GOOGLE_CLOUD_LOCATION=us-east5

# Logging Configuration
GEMINI_SHEETS_LOG_LEVEL=INFO
GEMINI_SHEETS_LOG_FILE=logs/app.log

# Gemini AI Configuration
GEMINI_SHEETS_GEMINI_MODEL=gemini-2.5-pro
GEMINI_SHEETS_GEMINI_MAX_RETRIES=3
```
- `GOOGLE_APPLICATION_CREDENTIALS`: Path to service account key (optional, uses ADC by default)

### Spreadsheet Access

The integration uses Google Cloud Application Default Credentials, which means:
- It works with any spreadsheet you have access to
- No need to share spreadsheets with service accounts for personal use
- Seamless integration with your existing Google account

## MCP Server Integration

This project works alongside the MCP Google Sheets server. The complete setup includes:

1. **MCP Server**: Handles Google Sheets operations via JSON-RPC
2. **Claude Desktop**: Provides the MCP client interface
3. **Gemini Integration**: Adds AI-powered data analysis capabilities

### Claude Desktop Configuration

Add to your `claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "google-sheets": {
      "command": "/path/to/uvx",
      "args": ["mcp-google-sheets@latest"],
      "env": {}
    }
  }
}
```

## Sample Data

The integration includes a test spreadsheet with employee data:

| Name | Age | City | Department | Salary |
|------|-----|------|------------|--------|
| Alice Johnson | 28 | New York | Engineering | 75000 |
| Bob Smith | 34 | San Francisco | Marketing | 65000 |
| Carol Davis | 29 | Chicago | Engineering | 80000 |
| ... | ... | ... | ... | ... |

## Troubleshooting

### Common Issues

1. **Authentication Errors**:
   ```bash
   gcloud auth application-default login --scopes=https://www.googleapis.com/auth/cloud-platform,https://www.googleapis.com/auth/spreadsheets,https://www.googleapis.com/auth/drive
   ```

2. **Vertex AI Errors**:
   - Verify your Google Cloud Project ID is correct
   - Ensure Vertex AI API is enabled in your project
   - Check your project has proper billing enabled
   - Verify you're using a supported region

3. **Spreadsheet Access**:
   - Verify the spreadsheet ID is correct
   - Ensure you have read access to the spreadsheet
   - Check that the sheet name exists

## 🏗️ Project Structure

```
gemini-with-sheets/
├── gemini_sheets_integration/     # Main package
│   ├── __init__.py
│   ├── config.py                  # Configuration management
│   ├── exceptions.py              # Custom exceptions
│   ├── integration.py             # Main integration class
│   ├── logging_config.py          # Logging setup
│   ├── prompts.py                 # AI prompt templates
│   ├── cli.py                     # Command-line interface
│   └── services/                  # Service modules
│       ├── __init__.py
│       ├── google_sheets_service.py
│       ├── gemini_service.py
│       └── data_processor.py
├── tests/                         # Unit tests
│   ├── __init__.py
│   ├── test_config.py
│   ├── test_data_processor.py
│   └── test_integration.py
├── main.py                        # Production entry point
├── test-main.py                   # Original test script
├── pyproject.toml                 # Project configuration
├── pytest.ini                    # Test configuration
├── .env.example                   # Environment template
└── README.md                      # This file
```

## 🧪 Testing

Run the test suite:

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=gemini_sheets_integration

# Run specific test file
pytest tests/test_integration.py

# Run with verbose output
pytest -v
```

## 🚀 Development

### Setting up Development Environment

1. **Install development dependencies:**
```bash
uv sync --group dev
```

2. **Run code formatting:**
```bash
black gemini_sheets_integration/
isort gemini_sheets_integration/
```

3. **Run type checking:**
```bash
mypy gemini_sheets_integration/
```

4. **Run linting:**
```bash
flake8 gemini_sheets_integration/
```

## 📊 Analysis Types

### GCP Security Analysis
Analyzes GCP infrastructure data for security compliance and best practices:
- IAM configurations
- Network security
- Resource configurations
- Compliance violations

### Financial Analysis
Analyzes financial data and metrics:
- Cost optimization opportunities
- Budget analysis
- Spending patterns
- ROI calculations

### Operational Analysis
Analyzes operational metrics and performance:
- Resource utilization
- Performance bottlenecks
- Operational efficiency
- Process improvements

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Add tests for new functionality
5. Run the test suite (`pytest`)
6. Commit your changes (`git commit -m 'Add amazing feature'`)
7. Push to the branch (`git push origin feature/amazing-feature`)
8. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Google Cloud team for Vertex AI and Google Sheets APIs
- Google AI team for Gemini AI capabilities
- The Python community for excellent libraries and tools

### Debug Mode

Set environment variable for verbose logging:
```bash
export DEBUG=1
uv run main.py
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Related Projects

- [MCP Google Sheets Server](https://github.com/xing5/mcp-google-sheets)
- [Google Generative AI Python SDK](https://github.com/google/generative-ai-python)
- [Google API Python Client](https://github.com/googleapis/google-api-python-client)