"""
Logging configuration for Gemini Sheets Integration.

This module sets up structured logging with proper formatting,
levels, and output destinations.
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional

from .config import Settings


def setup_logging(settings: Settings) -> logging.Logger:
    """
    Set up application logging based on configuration.
    
    Args:
        settings: Application settings containing logging configuration
        
    Returns:
        Configured logger instance
    """
    # Create logger
    logger = logging.getLogger("gemini_sheets_integration")
    logger.setLevel(getattr(logging, settings.log_level))
    
    # Clear any existing handlers
    logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter(settings.log_format)
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, settings.log_level))
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler (if specified)
    if settings.log_file:
        log_path = Path(settings.log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_path,
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5
        )
        file_handler.setLevel(getattr(logging, settings.log_level))
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def get_logger(name: Optional[str] = None) -> logging.Logger:
    """
    Get a logger instance.
    
    Args:
        name: Logger name (defaults to module name)
        
    Returns:
        Logger instance
    """
    if name:
        return logging.getLogger(f"gemini_sheets_integration.{name}")
    return logging.getLogger("gemini_sheets_integration")
