"""
Custom exceptions for Gemini Sheets Integration.

This module defines all custom exceptions used throughout the application
for better error handling and debugging.
"""


class GeminiSheetsError(Exception):
    """Base exception for all Gemini Sheets Integration errors."""
    
    def __init__(self, message: str, details: str = None):
        self.message = message
        self.details = details
        super().__init__(self.message)
    
    def __str__(self):
        if self.details:
            return f"{self.message}: {self.details}"
        return self.message


class ConfigurationError(GeminiSheetsError):
    """Raised when there's a configuration error."""
    pass


class GoogleSheetsError(GeminiSheetsError):
    """Raised when there's an error with Google Sheets operations."""
    pass


class GeminiAIError(GeminiSheetsError):
    """Raised when there's an error with Gemini AI operations."""
    pass


class ValidationError(GeminiSheetsError):
    """Raised when input validation fails."""
    pass


class AuthenticationError(GeminiSheetsError):
    """Raised when authentication fails."""
    pass


class DataProcessingError(GeminiSheetsError):
    """Raised when data processing fails."""
    pass
