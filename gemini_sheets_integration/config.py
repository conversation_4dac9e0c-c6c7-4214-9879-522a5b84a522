"""
Configuration management for Gemini Sheets Integration.

This module handles all configuration settings using Pydantic Settings
for type validation and environment variable support.
"""

import os
from typing import List, Optional
from pydantic import BaseSettings, Field, validator


class Settings(BaseSettings):
    """Application configuration settings."""
    
    # Google Cloud Configuration
    google_cloud_project: str = Field(
        default="searce-playground-v2",
        description="Google Cloud Project ID"
    )
    google_cloud_location: str = Field(
        default="us-east5", 
        description="Google Cloud region for Gemini AI"
    )
    
    # Google Sheets Configuration
    google_sheets_scopes: List[str] = Field(
        default=[
            'https://www.googleapis.com/auth/spreadsheets',
            'https://www.googleapis.com/auth/drive.readonly'
        ],
        description="Google Sheets API scopes"
    )
    
    # Application Configuration
    default_summary_sheet_name: str = Field(
        default="Gemini_Analysis_Summary",
        description="Default name for analysis summary sheet"
    )
    
    # Logging Configuration
    log_level: str = Field(
        default="INFO",
        description="Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)"
    )
    log_format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="Log message format"
    )
    log_file: Optional[str] = Field(
        default=None,
        description="Log file path (if None, logs to console only)"
    )
    
    # Gemini AI Configuration
    gemini_model: str = Field(
        default="gemini-2.5-pro",
        description="Gemini AI model to use"
    )
    gemini_max_retries: int = Field(
        default=3,
        description="Maximum number of retries for Gemini API calls"
    )
    gemini_timeout: int = Field(
        default=60,
        description="Timeout in seconds for Gemini API calls"
    )
    
    # Output Configuration
    save_response_to_file: bool = Field(
        default=True,
        description="Whether to save Gemini responses to file"
    )
    response_file_path: str = Field(
        default="response.txt",
        description="Path to save Gemini responses"
    )
    
    @validator('log_level')
    def validate_log_level(cls, v):
        """Validate log level is one of the standard levels."""
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'log_level must be one of {valid_levels}')
        return v.upper()
    
    @validator('google_cloud_project')
    def validate_project_id(cls, v):
        """Validate Google Cloud project ID format."""
        if not v or len(v) < 6:
            raise ValueError('google_cloud_project must be at least 6 characters')
        return v
    
    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        # Allow environment variables to override settings
        env_prefix = "GEMINI_SHEETS_"


def get_settings() -> Settings:
    """Get application settings instance."""
    return Settings()
