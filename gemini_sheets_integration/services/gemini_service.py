"""
Gemini AI service for generating content and analysis.

This module provides a clean interface for interacting with Google's
Gemini AI through the Vertex AI platform.
"""

import time
from typing import Op<PERSON>
from google import genai

from ..config import Settings
from ..exceptions import GeminiAIError, AuthenticationError
from ..logging_config import get_logger


class GeminiService:
    """Service for Gemini AI operations."""
    
    def __init__(self, settings: Settings):
        """
        Initialize Gemini AI service.
        
        Args:
            settings: Application settings
        """
        self.settings = settings
        self.logger = get_logger("gemini")
        self.client = None
        self._initialize_client()
    
    def _initialize_client(self) -> None:
        """Initialize Gemini AI client."""
        try:
            self.client = genai.Client(
                vertexai=True,
                project=self.settings.google_cloud_project,
                location=self.settings.google_cloud_location
            )
            
            self.logger.info(
                f"Gemini AI initialized | Project: {self.settings.google_cloud_project} "
                f"| Location: {self.settings.google_cloud_location}"
            )
            
        except Exception as error:
            self.logger.error(f"Failed to initialize Gemini AI: {error}")
            raise AuthenticationError(
                "Failed to authenticate with Gemini AI",
                str(error)
            )
    
    def generate_content(self, prompt: str) -> str:
        """
        Generate content using Gemini AI.
        
        Args:
            prompt: The prompt to send to Gemini
            
        Returns:
            Generated content as string
            
        Raises:
            GeminiAIError: If content generation fails
        """
        if not self.client:
            raise GeminiAIError("Gemini client not initialized")
        
        for attempt in range(self.settings.gemini_max_retries):
            try:
                self.logger.debug(f"Generating content (attempt {attempt + 1})")
                
                response = self.client.models.generate_content(
                    model=self.settings.gemini_model,
                    contents=prompt
                )
                
                if not response.text:
                    raise GeminiAIError("Empty response from Gemini AI")
                
                self.logger.info(
                    f"Successfully generated content "
                    f"({len(response.text)} characters)"
                )
                
                return response.text
                
            except Exception as error:
                self.logger.warning(
                    f"Attempt {attempt + 1} failed: {error}"
                )
                
                if attempt == self.settings.gemini_max_retries - 1:
                    self.logger.error(
                        f"All {self.settings.gemini_max_retries} attempts failed"
                    )
                    raise GeminiAIError(
                        "Failed to generate content after all retries",
                        str(error)
                    )
                
                # Exponential backoff
                wait_time = 2 ** attempt
                self.logger.info(f"Waiting {wait_time} seconds before retry")
                time.sleep(wait_time)
        
        # This should never be reached, but just in case
        raise GeminiAIError("Unexpected error in content generation")
    
    def analyze_spreadsheet_data(self, data_text: str, prompt_template: str) -> str:
        """
        Analyze spreadsheet data using a specific prompt template.
        
        Args:
            data_text: Formatted spreadsheet data
            prompt_template: Template for the analysis prompt
            
        Returns:
            Analysis results as string
            
        Raises:
            GeminiAIError: If analysis fails
        """
        try:
            # Format the prompt with the data
            full_prompt = prompt_template.format(data_text=data_text)
            
            self.logger.info("Starting spreadsheet data analysis")
            result = self.generate_content(full_prompt)
            
            self.logger.info("Spreadsheet analysis completed successfully")
            return result
            
        except Exception as error:
            self.logger.error(f"Error analyzing spreadsheet data: {error}")
            raise GeminiAIError(
                "Failed to analyze spreadsheet data",
                str(error)
            )
