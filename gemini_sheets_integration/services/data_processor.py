"""
Data processing service for formatting and parsing data.

This module handles all data transformation operations including
formatting spreadsheet data for analysis and parsing AI responses.
"""

from typing import Any, Dict, List
from pathlib import Path

from ..config import Settings
from ..exceptions import DataProcessingError
from ..logging_config import get_logger


class DataProcessor:
    """Service for data processing operations."""
    
    def __init__(self, settings: Settings):
        """
        Initialize data processor.
        
        Args:
            settings: Application settings
        """
        self.settings = settings
        self.logger = get_logger("data_processor")
    
    def format_spreadsheet_data(self, data: Dict[str, Any]) -> str:
        """
        Format spreadsheet data for analysis.
        
        Args:
            data: Raw spreadsheet data from Google Sheets service
            
        Returns:
            Formatted string suitable for AI analysis
            
        Raises:
            DataProcessingError: If formatting fails
        """
        try:
            lines = [
                f"Spreadsheet: {data['spreadsheet_title']}",
                f"Total Rows: {data['total_rows']}",
                f"Sheet Count: {data['sheet_count']}",
                f"Ranges: {', '.join(data['ranges'])}",
                ""
            ]
            
            for range_name, values in data['data'].items():
                lines.append(f"Data from {range_name}:")
                
                for i, row in enumerate(values):
                    # Handle None values and convert to strings
                    row_str = [str(cell) if cell is not None else '' for cell in row]
                    lines.append(f"Row {i+1}: {', '.join(row_str)}")
                
                lines.append("")
            
            formatted_data = '\n'.join(lines)
            
            self.logger.info(
                f"Formatted spreadsheet data: {len(lines)} lines, "
                f"{len(formatted_data)} characters"
            )
            
            return formatted_data
            
        except Exception as error:
            self.logger.error(f"Error formatting spreadsheet data: {error}")
            raise DataProcessingError(
                "Failed to format spreadsheet data",
                str(error)
            )
    
    def parse_pipe_delimited_response(self, response_text: str) -> List[List[str]]:
        """
        Parse pipe-delimited AI response into structured data.
        
        Args:
            response_text: Raw response text from AI
            
        Returns:
            List of rows suitable for spreadsheet writing
            
        Raises:
            DataProcessingError: If parsing fails
        """
        try:
            lines = response_text.strip().split('\n')
            
            # Standard headers for the analysis
            headers = [
                "Resource Type", 
                "Category", 
                "Description", 
                "Current State or Count", 
                "Assessment", 
                "Recommendation (if needed)", 
                "Priority"
            ]
            
            parsed_data = [headers]
            
            for line in lines:
                if '|' in line:
                    # Split by pipe and clean up
                    row = [cell.strip() for cell in line.split('|')]
                    
                    # Ensure row has exactly 7 columns
                    while len(row) < 7:
                        row.append('')
                    
                    # Truncate if too long
                    row = row[:7]
                    
                    parsed_data.append(row)
            
            self.logger.info(
                f"Parsed {len(parsed_data) - 1} data rows from AI response"
            )
            
            return parsed_data
            
        except Exception as error:
            self.logger.error(f"Error parsing AI response: {error}")
            raise DataProcessingError(
                "Failed to parse AI response",
                str(error)
            )
    
    def save_response_to_file(self, response_text: str, file_path: str = None) -> bool:
        """
        Save AI response to a file.
        
        Args:
            response_text: The response text to save
            file_path: Optional custom file path
            
        Returns:
            True if successful
            
        Raises:
            DataProcessingError: If saving fails
        """
        if not self.settings.save_response_to_file:
            self.logger.debug("Response file saving is disabled")
            return False
        
        try:
            output_path = Path(file_path or self.settings.response_file_path)
            
            # Create directory if it doesn't exist
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(response_text)
            
            self.logger.info(f"Saved AI response to: {output_path}")
            return True
            
        except Exception as error:
            self.logger.error(f"Error saving response to file: {error}")
            raise DataProcessingError(
                f"Failed to save response to file",
                str(error)
            )
    
    def validate_spreadsheet_id(self, spreadsheet_id: str) -> bool:
        """
        Validate Google Sheets spreadsheet ID format.
        
        Args:
            spreadsheet_id: The spreadsheet ID to validate
            
        Returns:
            True if valid
            
        Raises:
            DataProcessingError: If validation fails
        """
        if not spreadsheet_id:
            raise DataProcessingError("Spreadsheet ID cannot be empty")
        
        if len(spreadsheet_id) < 20:
            raise DataProcessingError(
                "Spreadsheet ID appears to be too short"
            )
        
        # Basic character validation
        if not all(c.isalnum() or c in '-_' for c in spreadsheet_id):
            raise DataProcessingError(
                "Spreadsheet ID contains invalid characters"
            )
        
        return True
