"""
Google Sheets service for reading from and writing to Google Sheets.

This module provides a clean interface for all Google Sheets operations
including reading data, creating sheets, and formatting.
"""

from typing import Any, Dict, List, Optional
from google.auth import default
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from ..config import Settings
from ..exceptions import GoogleSheetsError, AuthenticationError
from ..logging_config import get_logger


class GoogleSheetsService:
    """Service for Google Sheets operations."""
    
    def __init__(self, settings: Settings):
        """
        Initialize Google Sheets service.
        
        Args:
            settings: Application settings
        """
        self.settings = settings
        self.logger = get_logger("google_sheets")
        self.service = None
        self._initialize_service()
    
    def _initialize_service(self) -> None:
        """Initialize Google Sheets API service."""
        try:
            credentials, project = default(scopes=self.settings.google_sheets_scopes)
            self.service = build('sheets', 'v4', credentials=credentials)
            self.logger.info(f"Google Sheets API initialized for project: {project}")
        except Exception as error:
            self.logger.error(f"Failed to initialize Google Sheets API: {error}")
            raise AuthenticationError(
                "Failed to authenticate with Google Sheets API",
                str(error)
            )
    
    def read_spreadsheet(
        self, 
        spreadsheet_id: str, 
        ranges: Optional[List[str]] = None, 
        sheet_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Read data from a Google Spreadsheet.
        
        Args:
            spreadsheet_id: The ID of the spreadsheet
            ranges: Specific ranges to read (optional)
            sheet_id: Specific sheet ID to read (optional)
            
        Returns:
            Dictionary containing spreadsheet data and metadata
            
        Raises:
            GoogleSheetsError: If reading fails
        """
        try:
            # Get spreadsheet metadata
            spreadsheet = self.service.spreadsheets().get(
                spreadsheetId=spreadsheet_id
            ).execute()
            
            title = spreadsheet.get('properties', {}).get('title', 'Unknown')
            sheets = spreadsheet.get('sheets', [])
            
            # Determine target ranges
            target_ranges = self._determine_ranges(ranges, sheet_id, sheets)
            
            # Read data from ranges
            all_data = {}
            for range_name in target_ranges:
                result = self.service.spreadsheets().values().get(
                    spreadsheetId=spreadsheet_id,
                    range=range_name
                ).execute()
                all_data[range_name] = result.get('values', [])
            
            response_data = {
                'spreadsheet_title': title,
                'ranges': target_ranges,
                'data': all_data,
                'total_rows': sum(len(v) for v in all_data.values()),
                'sheet_count': len(sheets)
            }
            
            self.logger.info(
                f"Successfully read spreadsheet '{title}' "
                f"({response_data['total_rows']} rows, {len(sheets)} sheets)"
            )
            
            return response_data
            
        except HttpError as error:
            self.logger.error(f"HTTP error reading spreadsheet: {error}")
            raise GoogleSheetsError(
                f"HTTP error reading spreadsheet {spreadsheet_id}",
                str(error)
            )
        except Exception as error:
            self.logger.error(f"Unexpected error reading spreadsheet: {error}")
            raise GoogleSheetsError(
                f"Failed to read spreadsheet {spreadsheet_id}",
                str(error)
            )
    
    def _determine_ranges(
        self, 
        ranges: Optional[List[str]], 
        sheet_id: Optional[int], 
        sheets: List[Dict]
    ) -> List[str]:
        """Determine which ranges to read based on parameters."""
        if ranges:
            return ranges
        
        if sheet_id is not None:
            target_sheet = next(
                (s for s in sheets if s['properties']['sheetId'] == sheet_id), 
                None
            )
            if not target_sheet:
                raise GoogleSheetsError(f"Sheet ID {sheet_id} not found")
            return [target_sheet['properties']['title']]
        
        # Default to first sheet
        if not sheets:
            raise GoogleSheetsError("No sheets found in spreadsheet")
        return [sheets[0]['properties']['title']]

    def create_sheet(self, spreadsheet_id: str, sheet_name: str) -> bool:
        """
        Create a new sheet in the spreadsheet.

        Args:
            spreadsheet_id: The ID of the spreadsheet
            sheet_name: Name of the new sheet

        Returns:
            True if successful, False if sheet already exists

        Raises:
            GoogleSheetsError: If creation fails for other reasons
        """
        try:
            request_body = {
                'requests': [{
                    'addSheet': {
                        'properties': {
                            'title': sheet_name
                        }
                    }
                }]
            }

            self.service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_id,
                body=request_body
            ).execute()

            self.logger.info(f"Created new sheet: {sheet_name}")
            return True

        except HttpError as error:
            if "already exists" in str(error):
                self.logger.warning(f"Sheet '{sheet_name}' already exists")
                return True
            self.logger.error(f"Error creating sheet: {error}")
            raise GoogleSheetsError(
                f"Failed to create sheet '{sheet_name}'",
                str(error)
            )
        except Exception as error:
            self.logger.error(f"Unexpected error creating sheet: {error}")
            raise GoogleSheetsError(
                f"Failed to create sheet '{sheet_name}'",
                str(error)
            )

    def write_data(
        self,
        spreadsheet_id: str,
        data: List[List[str]],
        sheet_name: str,
        start_range: str = "A1"
    ) -> bool:
        """
        Write data to a sheet.

        Args:
            spreadsheet_id: The ID of the spreadsheet
            data: Data to write (list of rows)
            sheet_name: Name of the target sheet
            start_range: Starting cell (default: A1)

        Returns:
            True if successful

        Raises:
            GoogleSheetsError: If writing fails
        """
        try:
            # Clear existing data
            self.service.spreadsheets().values().clear(
                spreadsheetId=spreadsheet_id,
                range=f"{sheet_name}!A:Z"
            ).execute()

            # Write new data
            self.service.spreadsheets().values().update(
                spreadsheetId=spreadsheet_id,
                range=f"{sheet_name}!{start_range}",
                valueInputOption='RAW',
                body={"values": data}
            ).execute()

            self.logger.info(
                f"Successfully wrote {len(data)} rows to sheet '{sheet_name}'"
            )
            return True

        except Exception as error:
            self.logger.error(f"Error writing data to sheet: {error}")
            raise GoogleSheetsError(
                f"Failed to write data to sheet '{sheet_name}'",
                str(error)
            )

    def format_headers(self, spreadsheet_id: str, sheet_name: str) -> None:
        """
        Apply formatting to sheet headers.

        Args:
            spreadsheet_id: The ID of the spreadsheet
            sheet_name: Name of the sheet to format

        Raises:
            GoogleSheetsError: If formatting fails
        """
        try:
            # Get sheet ID
            spreadsheet = self.service.spreadsheets().get(
                spreadsheetId=spreadsheet_id
            ).execute()

            sheet_id = next(
                s['properties']['sheetId']
                for s in spreadsheet['sheets']
                if s['properties']['title'] == sheet_name
            )

            # Format requests
            requests = [
                {
                    "repeatCell": {
                        "range": {
                            "sheetId": sheet_id,
                            "startRowIndex": 0,
                            "endRowIndex": 1
                        },
                        "cell": {
                            "userEnteredFormat": {
                                "backgroundColor": {
                                    "red": 0.8,
                                    "green": 0.94,
                                    "blue": 0.8
                                },
                                "textFormat": {"bold": True},
                                "horizontalAlignment": "CENTER"
                            }
                        },
                        "fields": "userEnteredFormat(backgroundColor,textFormat,horizontalAlignment)"
                    }
                },
                {
                    "setBasicFilter": {
                        "filter": {
                            "range": {
                                "sheetId": sheet_id,
                                "startRowIndex": 0,
                                "endRowIndex": 1,
                                "startColumnIndex": 0,
                                "endColumnIndex": 7
                            }
                        }
                    }
                }
            ]

            self.service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_id,
                body={"requests": requests}
            ).execute()

            self.logger.info(f"Applied header formatting to sheet: {sheet_name}")

        except Exception as error:
            self.logger.error(f"Error formatting headers: {error}")
            raise GoogleSheetsError(
                f"Failed to format headers for sheet '{sheet_name}'",
                str(error)
            )
