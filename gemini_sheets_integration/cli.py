"""
Command-line interface for Gemini Sheets Integration.

This module provides a user-friendly CLI for running different types
of analysis workflows.
"""

import sys
from typing import Optional

import click
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn

from .config import get_settings
from .integration import GeminiSheetsIntegration
from .logging_config import setup_logging
from .exceptions import GeminiSheetsError


console = Console()


def setup_app():
    """Set up the application with logging and configuration."""
    settings = get_settings()
    logger = setup_logging(settings)
    return settings, logger


@click.group()
@click.version_option(version="1.0.0")
def cli():
    """
    Gemini Sheets Integration CLI
    
    A production-ready tool for integrating Google Gemini AI with Google Sheets
    for automated analysis and reporting.
    """
    pass


@cli.command("gcp-security")
@click.argument('spreadsheet_id')
@click.option('--sheet-name', '-s', help='Specific sheet to analyze')
@click.option('--output-sheet', '-o', help='Name for output sheet')
@click.option('--project', '-p', help='Google Cloud project ID')
@click.option('--location', '-l', help='Google Cloud location')
def gcp_security(
    spreadsheet_id: str,
    sheet_name: Optional[str],
    output_sheet: Optional[str],
    project: Optional[str],
    location: Optional[str]
):
    """
    Run GCP security analysis on spreadsheet data.
    
    SPREADSHEET_ID: The Google Sheets spreadsheet ID to analyze
    """
    try:
        settings, logger = setup_app()
        
        # Override settings if provided
        if project:
            settings.google_cloud_project = project
        if location:
            settings.google_cloud_location = location
        
        console.print(f"[bold blue]🚀 Starting GCP Security Analysis[/bold blue]")
        console.print(f"Spreadsheet ID: {spreadsheet_id}")
        console.print(f"Project: {settings.google_cloud_project}")
        console.print(f"Location: {settings.google_cloud_location}")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            
            task = progress.add_task("Initializing services...", total=None)
            integration = GeminiSheetsIntegration(settings)
            
            progress.update(task, description="Running analysis...")
            success = integration.run_gcp_security_analysis(
                spreadsheet_id=spreadsheet_id,
                sheet_name=sheet_name,
                output_sheet_name=output_sheet
            )
            
            progress.update(task, description="Analysis complete!", completed=True)
        
        if success:
            console.print(f"[bold green]✅ Analysis completed successfully![/bold green]")
            output_name = output_sheet or settings.default_summary_sheet_name
            console.print(f"Results written to sheet: [bold]{output_name}[/bold]")
        else:
            console.print(f"[bold red]❌ Analysis failed[/bold red]")
            sys.exit(1)
            
    except GeminiSheetsError as error:
        console.print(f"[bold red]❌ Error: {error.message}[/bold red]")
        if error.details:
            console.print(f"Details: {error.details}")
        sys.exit(1)
    except Exception as error:
        console.print(f"[bold red]❌ Unexpected error: {error}[/bold red]")
        sys.exit(1)


@cli.command()
@click.argument('spreadsheet_id')
@click.option('--type', '-t', 'analysis_type', 
              type=click.Choice(['general', 'financial', 'operational']),
              default='general', help='Type of analysis to perform')
@click.option('--sheet-name', '-s', help='Specific sheet to analyze')
@click.option('--output-sheet', '-o', help='Name for output sheet')
@click.option('--project', '-p', help='Google Cloud project ID')
@click.option('--location', '-l', help='Google Cloud location')
def analyze(
    spreadsheet_id: str,
    analysis_type: str,
    sheet_name: Optional[str],
    output_sheet: Optional[str],
    project: Optional[str],
    location: Optional[str]
):
    """
    Run custom analysis on spreadsheet data.
    
    SPREADSHEET_ID: The Google Sheets spreadsheet ID to analyze
    """
    try:
        settings, logger = setup_app()
        
        # Override settings if provided
        if project:
            settings.google_cloud_project = project
        if location:
            settings.google_cloud_location = location
        
        console.print(f"[bold blue]🚀 Starting {analysis_type.title()} Analysis[/bold blue]")
        console.print(f"Spreadsheet ID: {spreadsheet_id}")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            
            task = progress.add_task("Initializing services...", total=None)
            integration = GeminiSheetsIntegration(settings)
            
            progress.update(task, description="Running analysis...")
            success = integration.run_custom_analysis(
                spreadsheet_id=spreadsheet_id,
                analysis_type=analysis_type,
                sheet_name=sheet_name,
                output_sheet_name=output_sheet
            )
            
            progress.update(task, description="Analysis complete!", completed=True)
        
        if success:
            console.print(f"[bold green]✅ Analysis completed successfully![/bold green]")
            output_name = output_sheet or f"{analysis_type.title()}_Analysis"
            console.print(f"Results written to sheet: [bold]{output_name}[/bold]")
        else:
            console.print(f"[bold red]❌ Analysis failed[/bold red]")
            sys.exit(1)
            
    except GeminiSheetsError as error:
        console.print(f"[bold red]❌ Error: {error.message}[/bold red]")
        if error.details:
            console.print(f"Details: {error.details}")
        sys.exit(1)
    except Exception as error:
        console.print(f"[bold red]❌ Unexpected error: {error}[/bold red]")
        sys.exit(1)


@cli.command()
@click.argument('spreadsheet_id')
def info(spreadsheet_id: str):
    """
    Get information about a spreadsheet.
    
    SPREADSHEET_ID: The Google Sheets spreadsheet ID
    """
    try:
        settings, logger = setup_app()
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            
            task = progress.add_task("Getting spreadsheet info...", total=None)
            integration = GeminiSheetsIntegration(settings)
            
            info_data = integration.get_spreadsheet_info(spreadsheet_id)
            progress.update(task, description="Info retrieved!", completed=True)
        
        # Display information in a nice table
        table = Table(title="Spreadsheet Information")
        table.add_column("Property", style="cyan")
        table.add_column("Value", style="green")
        
        table.add_row("Title", info_data['spreadsheet_title'])
        table.add_row("Total Rows", str(info_data['total_rows']))
        table.add_row("Sheet Count", str(info_data['sheet_count']))
        table.add_row("Ranges", ", ".join(info_data['ranges']))
        
        console.print(table)
        
    except GeminiSheetsError as error:
        console.print(f"[bold red]❌ Error: {error.message}[/bold red]")
        if error.details:
            console.print(f"Details: {error.details}")
        sys.exit(1)
    except Exception as error:
        console.print(f"[bold red]❌ Unexpected error: {error}[/bold red]")
        sys.exit(1)


def main():
    """Main entry point for the CLI."""
    cli()


if __name__ == "__main__":
    main()
