"""
Main integration class for Gemini Sheets Integration.

This module provides the high-level interface for the complete
integration workflow between Google Sheets and Gemini AI.
"""

from typing import Optional

from .config import Settings
from .exceptions import GeminiSheetsError, ValidationError
from .logging_config import get_logger
from .services import GoogleSheetsService, GeminiService, DataProcessor
from .prompts import get_gcp_security_analysis_prompt, get_custom_analysis_prompt


class GeminiSheetsIntegration:
    """
    Main integration class for Gemini AI and Google Sheets.
    
    This class orchestrates the complete workflow:
    1. Reading data from Google Sheets
    2. Processing and formatting the data
    3. Analyzing with Gemini AI
    4. Writing results back to Google Sheets
    """
    
    def __init__(self, settings: Optional[Settings] = None):
        """
        Initialize the integration.
        
        Args:
            settings: Application settings (uses defaults if None)
        """
        self.settings = settings or Settings()
        self.logger = get_logger("integration")
        
        # Initialize services
        self.sheets_service = GoogleSheetsService(self.settings)
        self.gemini_service = GeminiService(self.settings)
        self.data_processor = DataProcessor(self.settings)
        
        self.logger.info("Gemini Sheets Integration initialized successfully")
    
    def run_gcp_security_analysis(
        self, 
        spreadsheet_id: str,
        sheet_name: Optional[str] = None,
        output_sheet_name: Optional[str] = None
    ) -> bool:
        """
        Run a complete GCP security analysis workflow.
        
        Args:
            spreadsheet_id: Google Sheets spreadsheet ID
            sheet_name: Specific sheet to analyze (optional)
            output_sheet_name: Name for output sheet (optional)
            
        Returns:
            True if successful
            
        Raises:
            GeminiSheetsError: If the workflow fails
        """
        try:
            # Validate inputs
            self.data_processor.validate_spreadsheet_id(spreadsheet_id)
            
            output_sheet = output_sheet_name or self.settings.default_summary_sheet_name
            
            self.logger.info(f"Starting GCP security analysis for spreadsheet: {spreadsheet_id}")
            
            # Step 1: Read data from spreadsheet
            self.logger.info("Reading data from spreadsheet...")
            sheet_data = self.sheets_service.read_spreadsheet(
                spreadsheet_id=spreadsheet_id,
                ranges=[sheet_name] if sheet_name else None
            )
            
            # Step 2: Format data for analysis
            self.logger.info("Formatting data for analysis...")
            formatted_data = self.data_processor.format_spreadsheet_data(sheet_data)
            
            # Step 3: Generate analysis with Gemini AI
            self.logger.info("Running Gemini AI analysis...")
            prompt = get_gcp_security_analysis_prompt(formatted_data)
            analysis_result = self.gemini_service.generate_content(prompt)
            
            # Step 4: Save response to file (if enabled)
            self.data_processor.save_response_to_file(analysis_result)
            
            # Step 5: Parse and write results to spreadsheet
            self.logger.info("Writing analysis results to spreadsheet...")
            parsed_data = self.data_processor.parse_pipe_delimited_response(analysis_result)
            
            # Create and write to output sheet
            self.sheets_service.create_sheet(spreadsheet_id, output_sheet)
            self.sheets_service.write_data(
                spreadsheet_id=spreadsheet_id,
                data=parsed_data,
                sheet_name=output_sheet
            )
            
            # Format the headers
            self.sheets_service.format_headers(spreadsheet_id, output_sheet)
            
            self.logger.info(
                f"GCP security analysis completed successfully. "
                f"Results written to sheet: {output_sheet}"
            )
            
            return True
            
        except Exception as error:
            self.logger.error(f"GCP security analysis failed: {error}")
            raise GeminiSheetsError(
                "Failed to complete GCP security analysis",
                str(error)
            )
    
    def run_custom_analysis(
        self,
        spreadsheet_id: str,
        analysis_type: str = "general",
        sheet_name: Optional[str] = None,
        output_sheet_name: Optional[str] = None
    ) -> bool:
        """
        Run a custom analysis workflow.
        
        Args:
            spreadsheet_id: Google Sheets spreadsheet ID
            analysis_type: Type of analysis (general, financial, operational)
            sheet_name: Specific sheet to analyze (optional)
            output_sheet_name: Name for output sheet (optional)
            
        Returns:
            True if successful
            
        Raises:
            GeminiSheetsError: If the workflow fails
        """
        try:
            # Validate inputs
            self.data_processor.validate_spreadsheet_id(spreadsheet_id)
            
            valid_types = ["general", "financial", "operational"]
            if analysis_type not in valid_types:
                raise ValidationError(
                    f"Invalid analysis type. Must be one of: {valid_types}"
                )
            
            output_sheet = output_sheet_name or f"{analysis_type.title()}_Analysis"
            
            self.logger.info(
                f"Starting {analysis_type} analysis for spreadsheet: {spreadsheet_id}"
            )
            
            # Step 1: Read data from spreadsheet
            sheet_data = self.sheets_service.read_spreadsheet(
                spreadsheet_id=spreadsheet_id,
                ranges=[sheet_name] if sheet_name else None
            )
            
            # Step 2: Format data for analysis
            formatted_data = self.data_processor.format_spreadsheet_data(sheet_data)
            
            # Step 3: Generate analysis with Gemini AI
            prompt = get_custom_analysis_prompt(formatted_data, analysis_type)
            analysis_result = self.gemini_service.generate_content(prompt)
            
            # Step 4: Save response to file (if enabled)
            self.data_processor.save_response_to_file(analysis_result)
            
            # Step 5: Parse and write results to spreadsheet
            parsed_data = self.data_processor.parse_pipe_delimited_response(analysis_result)
            
            # Create and write to output sheet
            self.sheets_service.create_sheet(spreadsheet_id, output_sheet)
            self.sheets_service.write_data(
                spreadsheet_id=spreadsheet_id,
                data=parsed_data,
                sheet_name=output_sheet
            )
            
            # Format the headers
            self.sheets_service.format_headers(spreadsheet_id, output_sheet)
            
            self.logger.info(
                f"{analysis_type.title()} analysis completed successfully. "
                f"Results written to sheet: {output_sheet}"
            )
            
            return True
            
        except Exception as error:
            self.logger.error(f"{analysis_type} analysis failed: {error}")
            raise GeminiSheetsError(
                f"Failed to complete {analysis_type} analysis",
                str(error)
            )
    
    def get_spreadsheet_info(self, spreadsheet_id: str) -> dict:
        """
        Get basic information about a spreadsheet.
        
        Args:
            spreadsheet_id: Google Sheets spreadsheet ID
            
        Returns:
            Dictionary with spreadsheet information
        """
        try:
            self.data_processor.validate_spreadsheet_id(spreadsheet_id)
            return self.sheets_service.read_spreadsheet(spreadsheet_id)
        except Exception as error:
            self.logger.error(f"Failed to get spreadsheet info: {error}")
            raise GeminiSheetsError(
                "Failed to get spreadsheet information",
                str(error)
            )
