def get_prompt(data_text: str) -> str:
    return f"""
    You are a Google Cloud security and compliance expert. Analyze the provided GCP infrastructure inventory {data_text} and generate a prioritized action list of all configurations that violate GCP best practices.

🎯 Goal
Produce a focused, Excel-compatible action list that exclusively identifies and details configurations that deviate from Google Cloud best practices. The output must be a practical to-do list for remediation.

Only include rows where a best practice is violated.

Omit all healthy, compliant, or purely informational items.

The output must be a direct list of required improvements for security and compliance.

✅ Output Format (Excel Action List)
Return each row in the following format:

Resource Type | Category | Description of Non-Compliance | Current State or Count | What can be improved to enhance security and compliance | Recommendation (Specific Action) | Priority (High/Medium/Low)

Do not include this header row in the response.

Use pipe delimiter (|) for Excel parsing.

Do not use bullet points or markdown.

Each row must represent a specific, actionable compliance gap.

Be quantitative and technically specific.

✅ Sample Output Rows (Illustrating Violations Only)
Compute Engine | Security | VMs configured with public IP addresses | 30 | Reduce attack surface by removing direct internet access | Move to private IPs and use IAP for access | High
IAM | Governance | IAM roles are assigned directly to individual users | 75 users | Centralize and simplify access management at scale | Migrate from individual user roles to group-based IAM | Medium
Cloud SQL | Networking | Instances are using public IPs for connectivity | 16 | Prevent direct database exposure and enforce private access | Use the Cloud SQL Auth Proxy or private IP connections | High
Cloud Storage | Optimization | Buckets are missing a data lifecycle policy | 24 buckets | Optimize storage costs for infrequently accessed data | Add lifecycle rules for transitioning data to colder storage | Medium
Firewall | Security | Ingress rules allow traffic from any source (0.0.0.0/0) | 9 rules | Minimize network attack surface from the public internet | Restrict source CIDR ranges to known, trusted IPs | High
Cloud Run | IAM | Services are running with the default service account | 22 | Enforce the principle of least privilege for serverless workloads | Assign custom, purpose-built service accounts for each service | High
Labels | Governance | High percentage of resources are missing labels | 90% unlabeled | Improve cost tracking, ownership, and automation capabilities | Implement and enforce a consistent labeling strategy via org policies | Medium
Logging | Monitoring | No organization-wide aggregated log sink is configured | 0 | Centralize security and audit logging for organization-wide visibility | Setup an aggregated log sink to a central BigQuery dataset or GCS bucket | High
Quotas | Resource Health | Resources are operating at >80% of their allocated quota | 13 quotas | Proactively prevent service disruptions from resource exhaustion | Configure alerts for quota thresholds and request increases where needed | High
Subnets | Networking | Subnets are configured without Private Google Access | 52 | Allow instances to securely reach Google APIs without external IPs | Enable Private Google Access on subnets hosting internal workloads | Medium

📁 Required Checks for Non-Compliance
Analyze the inventory data for the following specific violations. If a violation is found, generate a row for it.

IAM & Identity
Default Service Accounts: Any resource (VM, Cloud Function, Cloud Run, etc.) using a default service account.

Primitive Roles: Any use of roles/owner, roles/editor, or roles/viewer.

Direct User Permissions: Any IAM policy granting roles directly to user: principals.

Stale Service Account Keys: Any user-managed service account key older than 90 days.

Overly-Permissive Impersonation: Any broad assignment of iam.serviceAccountUser or iam.serviceAccountTokenCreator.

Networking, VPC & Firewalls
Unrestricted Ingress: Any firewall rule allowing ingress from 0.0.0.0/0.

Permissive Port Exposure: Any firewall rule exposing sensitive ports (22, 3389, 3306, etc.) to a wide range.

Public IP Exposure: Any VM, Cloud SQL instance, or GKE cluster with a public IP.

Default VPC Usage: Any project containing a default VPC.

Missing Private Google Access: Any subnet where privateIpGoogleAccess is false.

Disabled Firewall Logging: Any firewall rule with logging disabled.

Insecure Load Balancer Policies: Any HTTPS Load Balancer not using a recommended SSL policy.

Compute Engine & GKE
Disabled Shielded VM: Any VM without Secure Boot, vTPM, and Integrity Monitoring.

Legacy Metadata Endpoints: Any VM or GKE node allowing the v1beta1 metadata endpoint.

Disabled OS Login: Any project not using OS Login.

Disabled GKE Security Features: Any GKE cluster without Master Authorized Networks, Workload Identity, or Network Policy enabled.

Storage & Data Security
Public Cloud Storage Buckets: Any bucket accessible by allUsers or allAuthenticatedUsers.

Disabled Uniform Bucket-Level Access: Any bucket not enforcing UBLA.

Missing Data Protection: Any critical bucket without Object Versioning/Retention or any Cloud SQL instance without Deletion Protection.

Non-CMEK Encryption: Any critical resource using Google-managed keys instead of CMEK.

Unenforced SQL SSL: Any Cloud SQL instance not enforcing SSL connections.

Logging, Monitoring & Governance
Missing Centralized Logging: The absence of an active, organization-level log sink.

Missing Data Access Audit Logs: Any project without Data Access audit logs enabled for key services.

Standard Tier Security Command Center: If SCC tier is Standard instead of Premium.

Poor Labeling: A high percentage of key resources missing essential labels.

Unenforced Organization Policies: The absence of key constraints like compute.vmExternalIpAccess or storage.publicAccessPrevention.

🧠 Guidelines
ONLY INCLUDE ROWS FOR VIOLATIONS: If the inventory data shows a resource is configured according to best practices, do not include it in the output. Your response should be a list of problems to be solved.

Be Action-Oriented: The 'What can be improved...' column should state the goal, and the 'Recommendation' column should provide the specific technical action.

Prioritize Ruthlessly: Use High for direct security risks, Medium for compliance/cost/management issues, and Low for minor optimizations.
"""
