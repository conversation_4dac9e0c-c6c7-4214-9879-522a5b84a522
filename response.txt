Logging|Monitoring|Lack of a centralized, organization-wide log sink for security and audit trails.|264 log sinks distributed across projects instead of a single aggregated sink.|Centralize security and audit logging for organization-wide visibility and incident response.|Configure an organization-level log sink to export all audit logs and critical service logs to a central GCS bucket or BigQuery dataset for immutable, long-term retention and analysis.|High
Networking|Security|A large number of resources have Public IP addresses, significantly increasing the attack surface.|223 Public IPs (PIPs) across all projects, with 96 in `bri-tcd-prod` alone.|Reduce attack surface by removing direct internet access for internal workloads.|Audit all 223 Public IPs. For VMs, migrate to private IPs, using Cloud NAT for egress and IAP or bastion hosts for ingress. For Cloud SQL, use the Auth Proxy or private IP.|High
Cloud SQL|Networking|A high number of Cloud SQL instances in project `bri-tcd-prod` are at risk of public exposure.|40 Cloud SQL instances in a project with 96 Public IPs.|Prevent direct database exposure and enforce private, authenticated connections.|Audit all 40 Cloud SQL instances in `bri-tcd-prod`. Disable public IPs and enforce the use of the Cloud SQL Auth Proxy or private IP connections with SSL/TLS required.|High
Firewall|Security|Numerous firewall rules across multiple projects likely contain overly permissive ingress from any source (0.0.0.0/0).|488 rules in total; 87 in `concrete-plasma-244309`, 66 in `common-sharenet-prd-01`, 57 in `bri-tcd-prod`.|Minimize network attack surface from the public internet by adhering to the principle of least privilege.|Audit all 488 firewall rules, prioritizing the projects with the highest counts. Replace `0.0.0.0/0` with specific, known IP ranges, especially for sensitive ports like 22, 3389, 5432, 3306.|High
Monitoring|Alerting|Critical monitoring is absent across the majority of projects, preventing proactive issue detection.|119 total projects, but most have 0 Alert Policies. The `bri-tcd-prod` project has 44, indicating a lack of standardized alerting elsewhere.|Ensure proactive detection of performance issues, security events, and resource exhaustion before they cause outages.|Implement a baseline set of Cloud Monitoring alert policies for all projects, covering CPU/memory utilization, disk space, quota usage, and key security log entries.|High
IAM|Governance|Extensive use of IAM bindings increases the risk of primitive roles (Owner/Editor/Viewer) and direct user assignments.|1123 total IAM bindings, with high concentrations in `poc-ddb-mlops` (117), `concrete-plasma-244309` (87), and `bri-tcd-prod` (68).|Enforce least privilege, simplify access management, and improve the auditability of permissions.|Audit IAM policies in high-count projects. Replace primitive roles with predefined or custom roles and migrate direct user assignments to group-based permissions.|High
Cloud Storage|Security|A large number of Cloud Storage buckets may be publicly accessible or lack uniform access controls.|266 buckets in total, with 55 in `poc-ddb-mlops` and 40 in `bri-tcd-prod`.|Prevent unintentional data exposure from misconfigured bucket permissions.|Audit all 266 buckets for public exposure (`allUsers` or `allAuthenticatedUsers`). Enforce Uniform Bucket-Level Access on all buckets and enable the `storage.publicAccessPrevention` organization policy.|High
GKE|Security|GKE clusters may be missing key security hardening features like Workload Identity and Network Policies.|11 GKE clusters identified, with 3 in `ddb-kubecluster-prd-01`.|Harden the container environment, enforce least privilege for pods, and restrict control plane access.|Audit all 11 GKE clusters. Enforce the use of Workload Identity instead of node service accounts, enable Network Policies for pod-to-pod traffic control, and configure Master Authorized Networks.|High
Serverless|IAM|A significant number of Cloud Functions and Cloud Run services may be using the highly-privileged default service account.|57 Cloud Functions and 30 Cloud Run services exist, notably 27 functions in `bri-tcd-prod` and 14 run services in `ddb-poc-briacademy`.|Enforce the principle of least privilege for serverless workloads to limit potential blast radius.|Audit all Cloud Run and Cloud Function services. Replace the default service account with a dedicated, custom service account with minimal necessary permissions for each application.|High
Subnets|Networking|Subnets may not have Private Google Access enabled, forcing instances to use external IPs to reach Google APIs.|492 subnets exist, with high counts in `poc-ddb-mlops` (88), `bri-tcd-prod` (49), and `concrete-plasma-244309` (48).|Allow instances without external IPs to securely reach Google APIs and services, reducing the need for public IPs.|Audit subnets in projects with internal workloads. Enable Private Google Access on all subnets that host resources requiring access to Google APIs without a public IP.|Medium
IAM|Security|A large number of Service Accounts exist, increasing the risk of stale, unrotated user-managed keys.|312 Service Accounts in total, with 46 in `concrete-plasma-244309`, 39 in `common-sec-prd-01`, and 31 in `poc-ddb-mlops`.|Reduce the risk of credential compromise from leaked or stale keys.|Audit all user-managed service account keys. Implement a key rotation policy of 90 days and create alerts for non-compliant keys. Prioritize eliminating keys in favor of workload identity or service account attachment.|Medium
Governance|Organization Policy|Core security organization policies are likely not enforced, leading to inconsistent security postures across projects.|Violations like Public IPs and potential public buckets suggest policies like `compute.vmExternalIpAccess` and `storage.publicAccessPrevention` are not enforced.|Establish a consistent, secure baseline for all projects in the organization and prevent common misconfigurations.|Define and enforce an organization policy baseline, including `constraints/compute.vmExternalIpAccess` (deny), `constraints/storage.publicAccessPrevention` (enforce), and `constraints/iam.allowedPolicyMemberDomains`.|High