Multiple projects have excessive IAM bindings (e.g., 117, 87, 81) indicating widespread violations of least privilege.|High
The presence of 223 Public IPs across an estate with 253 VMs indicates a high-risk, internet-exposed architecture.|High
115 projects lack custom log sinks for exporting logs to a central security monitoring solution.|High
488 VPC firewall rules deployed across the environment create significant management complexity and increase the risk of misconfiguration.|Medium
109 out of 119 projects have no custom Cloud Monitoring alert policies configured.|Medium
Inconsistent project naming (e.g., 'concrete-plasma-244309', 'isg-playground') indicates the lack of an enforced governance process for project creation.|Medium
Multiple projects show a high ratio of service accounts to VMs (e.g., 46 SAs for 22 VMs), indicating a lack of service account governance.|Medium
58 legacy Container Registries are in use instead of the recommended Artifact Registry, missing enhanced security features.|Medium