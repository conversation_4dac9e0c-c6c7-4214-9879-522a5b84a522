Compute Engine|Networking|VMs are directly exposed to the internet via public IP addresses.|223 Public IPs assigned across 253 VMs|Reduce attack surface and enforce access through controlled gateways.|Remove public IPs from VMs where possible. Use Cloud NAT for egress and IAP or bastion hosts for ingress.|High
Cloud SQL|Networking|Cloud SQL instances are configured with public IP addresses, exposing databases directly to the internet.|80 Cloud SQL instances exist; many likely have public IPs|Prevent direct database exposure and enforce private, authenticated access.|Configure all Cloud SQL instances with private IPs and mandate connections via the Cloud SQL Auth Proxy.|High
Firewall|Security|Firewall rules likely exist that allow unrestricted ingress from any source (0.0.0.0/0), including for sensitive ports.|488 VPC Firewall Rules and 88 Firewall Policy Rules in total|Minimize network attack surface from the public internet by applying the principle of least privilege.|Audit all firewall rules. Replace `0.0.0.0/0` source ranges with specific, known IPs. Use IAP for management access instead of opening SSH/RDP ports.|High
Cloud Storage|Security|Buckets may be publicly accessible to `allUsers` or `allAuthenticatedUsers`.|266 Cloud Storage buckets|Prevent unintentional data exposure from misconfigured bucket permissions.|Enforce the `storage.publicAccessPrevention` organization policy. Audit all buckets and remove public principals.|High
Logging|Monitoring|Log data is fragmented across projects instead of being centralized, hindering organization-wide security analysis and incident response.|264 log sinks across 119 projects, indicating a decentralized approach and no single org-level sink.|Centralize security and audit logging for comprehensive visibility and long-term retention.|Configure an aggregated log sink at the organization level to export logs to a central BigQuery dataset or Cloud Storage bucket.|High
IAM|Governance|Basic roles (Owner, Editor, Viewer) are likely in use, granting excessive permissions beyond what is required.|1123 total IAM bindings|Enforce the principle of least privilege to reduce the impact of compromised credentials.|Audit IAM policies for `roles/owner`, `roles/editor`, and `roles/viewer`. Replace them with predefined or custom IAM roles.|High
IAM|Security|Compute Engine, Cloud Functions, and other resources are likely using broad-permissioned default service accounts.|253 VMs, 57 Functions, 30 Cloud Run services|Enforce the principle of least privilege for application and compute identities.|Audit resources for use of default service accounts. Create and assign dedicated, purpose-built service accounts with minimal permissions for each workload.|Medium
GKE|Security|GKE clusters may be running without key security features like private networking, Workload Identity, or Network Policies.|11 GKE clusters|Harden GKE clusters by enabling built-in security controls and reducing the attack surface.|Audit all GKE clusters. Enforce the use of private clusters, enable Workload Identity, configure Master Authorized Networks, and implement Network Policies.|Medium
Cloud SQL|Data Security|Cloud SQL instances may not be enforcing the use of SSL for client connections, allowing for potential data-in-transit interception.|80 Cloud SQL instances|Ensure all data in transit to SQL databases is encrypted.|Audit all 80 Cloud SQL instances and enable the "Allow only SSL connections" setting.|Medium
Cloud Storage & SQL|Data Security|Critical data may be at risk of accidental deletion or modification without proper protection features.|266 Storage Buckets, 80 Cloud SQL instances|Protect against accidental or malicious data loss.|Enable Object Versioning on critical Cloud Storage buckets. Enable Deletion Protection on all production Cloud SQL instances.|Medium
Subnets|Networking|Subnets hosting internal workloads may lack Private Google Access, forcing instances to use external IPs to reach Google APIs.|492 Subnets|Allow instances with only private IPs to securely reach Google APIs and services.|Review all subnets. For those hosting workloads without external IPs, enable Private Google Access.|Medium
Labels|Governance|A lack of consistent labeling across resources hinders cost allocation, automation, and incident response.|Thousands of resources across 119 projects with no specified labeling strategy|Improve cost tracking, ownership visibility, and automation capabilities.|Define and implement a mandatory labeling policy (e.g., for `owner`, `cost-center`, `environment`) and enforce it using organization policies.|Medium
Logging|Security|Data Access audit logs are likely not enabled, preventing visibility into who is accessing sensitive data.|Configuration not specified; disabled by default|Gain full audit trails for data access events on critical services like Cloud Storage and BigQuery.|Create an organization policy to enable Data Access audit logs (DATA_READ, DATA_WRITE) for all relevant services across all projects.|Medium
Load Balancing|Security|HTTPS Load Balancers may be using outdated or weak SSL policies.|315 LB Frontends, 249 SSL Certificates|Ensure traffic is encrypted using modern, secure TLS versions and ciphers.|Audit all target HTTPS proxies. Enforce a modern SSL policy, such as `MODERN` or a `RESTRICTED` custom policy, and disallow weak protocols.|Medium