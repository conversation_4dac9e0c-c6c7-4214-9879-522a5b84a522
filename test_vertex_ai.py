#!/usr/bin/env python3
"""
Test the new Vertex AI integration
"""

import os
import sys
from google.auth import default

def test_vertex_ai_setup():
    """Test Vertex AI setup and configuration"""
    print("🧪 Testing Vertex AI Integration")
    print("=" * 50)
    
    # Check environment variables
    project_id = os.getenv('GOOGLE_CLOUD_PROJECT') or os.getenv('GCLOUD_PROJECT')
    location = os.getenv('GOOGLE_CLOUD_LOCATION', 'us-central1')
    
    print(f"📋 Project ID: {project_id or 'Not set'}")
    print(f"📋 Location: {location}")
    
    if not project_id:
        print("❌ GOOGLE_CLOUD_PROJECT environment variable not set")
        print("Set with: export GOOGLE_CLOUD_PROJECT='your-project-id'")
        return False
    
    # Test authentication
    try:
        credentials, auth_project = default()
        print(f"✅ Authentication: Success")
        print(f"📋 Authenticated project: {auth_project}")
        
        if auth_project != project_id:
            print(f"⚠️  Warning: Environment project ({project_id}) differs from auth project ({auth_project})")
    except Exception as e:
        print(f"❌ Authentication failed: {e}")
        return False
    
    # Test Vertex AI client
    try:
        from google import genai
        
        client = genai.Client(
            vertexai=True,
            project=project_id,
            location=location
        )
        
        print(f"✅ Vertex AI client created successfully")
        
        # Test a simple generation (this will make an actual API call)
        print("🤖 Testing Gemini model...")
        try:
            response = client.models.generate_content(
                model="gemini-2.0-flash-exp",
                contents="Say 'Hello from Vertex AI!' in exactly 5 words."
            )
            print(f"✅ Model response: {response.text.strip()}")
            return True
            
        except Exception as e:
            print(f"❌ Model generation failed: {e}")
            print("This might be due to:")
            print("- Vertex AI API not enabled in your project")
            print("- Insufficient permissions")
            print("- Billing not set up")
            print("- Region not supported")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure google-genai is installed: uv add google-genai")
        return False
    except Exception as e:
        print(f"❌ Vertex AI client creation failed: {e}")
        return False

def test_sheets_integration():
    """Test the updated sheets integration"""
    print("\n📊 Testing Updated Sheets Integration")
    print("=" * 50)
    
    try:
        # Import the updated integration
        from main import GeminiSheetsIntegration
        
        # Get project info
        project_id = os.getenv('GOOGLE_CLOUD_PROJECT') or os.getenv('GCLOUD_PROJECT')
        location = os.getenv('GOOGLE_CLOUD_LOCATION', 'us-central1')
        
        if not project_id:
            print("❌ Cannot test integration without GOOGLE_CLOUD_PROJECT")
            return False
        
        # Initialize integration
        print("🚀 Initializing integration...")
        integration = GeminiSheetsIntegration(project_id=project_id, location=location)
        
        # Test spreadsheet access
        SPREADSHEET_ID = "1RtLehY7lHfJfYwZ7suGYWfBCHM7fRs108O9qVE0N-cs"
        
        print("📋 Testing spreadsheet access...")
        data = integration.get_spreadsheet_data(SPREADSHEET_ID)
        
        print(f"✅ Spreadsheet loaded: {data['spreadsheet_title']}")
        print(f"📏 Dimensions: {data['row_count']} rows × {data['column_count']} columns")
        
        # Test a simple query
        from main import format_data_for_analysis, query_data_with_gemini
        
        data_text = format_data_for_analysis(data)
        test_query = "How many rows of data are there?"
        
        print(f"\n🤖 Testing query: '{test_query}'")
        response = query_data_with_gemini(integration.gemini_client, data_text, test_query)
        print(f"✅ Response: {response.strip()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Vertex AI Integration Test Suite")
    print("=" * 60)
    
    # Check prerequisites
    print("📋 Prerequisites:")
    print("1. Set GOOGLE_CLOUD_PROJECT environment variable")
    print("2. Ensure Vertex AI API is enabled in your project")
    print("3. Have proper authentication (gcloud auth application-default login)")
    print("4. Billing must be enabled for Vertex AI usage")
    
    tests = [
        ("Vertex AI Setup", test_vertex_ai_setup),
        ("Sheets Integration", test_sheets_integration),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Test {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 TEST SUMMARY")
    print(f"{'='*60}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Vertex AI integration is working!")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
        print("\n💡 Common solutions:")
        print("- Enable Vertex AI API: gcloud services enable aiplatform.googleapis.com")
        print("- Set up billing in Google Cloud Console")
        print("- Verify project permissions")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    sys.exit(main())
