[project]
name = "gemini-sheets-integration"
version = "1.0.0"
description = "Production-ready Gemini AI and Google Sheets integration for GCP security analysis"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "google-api-python-client>=2.176.0",
    "google-auth>=2.40.3",
    "google-auth-httplib2>=0.2.0",
    "google-auth-oauthlib>=1.2.2",
    "google-genai>=1.24.0",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "click>=8.0.0",
    "rich>=13.0.0",
]

[project.scripts]
gemini-sheets = "gemini_sheets_integration.cli:main"

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.10.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ['py310']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
