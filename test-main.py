#!/usr/bin/env python3
"""
Gemini-Google Sheets Integration using MCP-style approach
Reads from Google Sheets, processes with Gemini, and writes back to sheet
"""

import os
from typing import Any, Dict, List, Optional
from google.auth import default
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from google import genai
from prompt import get_prompt

class MCPGeminiSheetsIntegration:
    """MCP-style integration class for Gemini AI and Google Sheets"""

    def __init__(self, project_id: Optional[str] = None, location: Optional[str] = None):
        self.sheets_service = None
        self.gemini_client = None
        self.project_id = project_id
        self.location = location
        self.setup_google_sheets()
        self.setup_gemini()

    def setup_google_sheets(self):
        try:
            credentials, project = default(scopes=[
                'https://www.googleapis.com/auth/spreadsheets',
                'https://www.googleapis.com/auth/drive.readonly'
            ])
            self.sheets_service = build('sheets', 'v4', credentials=credentials)
            print(f"✅ Google Sheets API initialized for project: {project}")
        except Exception as error:
            print(f"❌ Error setting up Google Sheets API: {error}")
            raise

    def setup_gemini(self):
        try:
            if not self.project_id:
                self.project_id = os.getenv('GOOGLE_CLOUD_PROJECT') or os.getenv('GCLOUD_PROJECT') or "searce-playground-v2"
                print(f"⚠️  Using default project: {self.project_id}")
            if not self.location:
                self.location = os.getenv('GOOGLE_CLOUD_LOCATION', 'us-east5')
            self.gemini_client = genai.Client(vertexai=True, project=self.project_id, location=self.location)
            print(f"✅ Gemini AI initialized | 📋 Project: {self.project_id} | 📍 Location: {self.location}")
        except Exception as error:
            print(f"❌ Error setting up Gemini AI: {error}")
            raise

    def gsheets_read(self, spreadsheet_id: str, ranges: Optional[List[str]] = None, sheet_id: Optional[int] = None) -> Dict[str, Any]:
        try:
            spreadsheet = self.sheets_service.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()
            title = spreadsheet.get('properties', {}).get('title', 'Unknown')
            sheets = spreadsheet.get('sheets', [])
            if ranges:
                target_ranges = ranges
            else:
                if sheet_id is not None:
                    target_sheet = next((s for s in sheets if s['properties']['sheetId'] == sheet_id), None)
                    if not target_sheet:
                        raise ValueError(f"Sheet ID {sheet_id} not found")
                    sheet_name = target_sheet['properties']['title']
                else:
                    sheet_name = sheets[0]['properties']['title']
                target_ranges = [sheet_name]

            all_data = {}
            for range_name in target_ranges:
                result = self.sheets_service.spreadsheets().values().get(
                    spreadsheetId=spreadsheet_id,
                    range=range_name
                ).execute()
                all_data[range_name] = result.get('values', [])

            return {
                'spreadsheet_title': title,
                'ranges': target_ranges,
                'data': all_data,
                'total_rows': sum(len(v) for v in all_data.values()),
                'sheet_count': len(sheets)
            }
        except HttpError as error:
            print(f"❌ HTTP Error: {error}")
            raise
        except Exception as error:
            print(f"❌ Error fetching spreadsheet data: {error}")
            raise

    def create_summary_sheet(self, spreadsheet_id: str, sheet_name: str = "Analysis_Summary") -> bool:
        try:
            request_body = {
                'requests': [{
                    'addSheet': {
                        'properties': {
                            'title': sheet_name
                        }
                    }
                }]
            }
            self.sheets_service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_id,
                body=request_body
            ).execute()
            print(f"✅ Created new sheet: {sheet_name}")
            return True
        except HttpError as error:
            if "already exists" in str(error):
                print(f"⚠️  Sheet '{sheet_name}' already exists")
                return True
            print(f"❌ Error creating sheet: {error}")
            return False

    def format_summary_sheet_headers(self, spreadsheet_id: str, sheet_name: str = "Analysis_Summary") -> None:
        try:
            spreadsheet = self.sheets_service.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()
            sheet_id = next(s['properties']['sheetId'] for s in spreadsheet['sheets'] if s['properties']['title'] == sheet_name)
            requests = [
                {
                    "repeatCell": {
                        "range": {"sheetId": sheet_id, "startRowIndex": 0, "endRowIndex": 1},
                        "cell": {
                            "userEnteredFormat": {
                                "backgroundColor": {"red": 0.8, "green": 0.94, "blue": 0.8},
                                "textFormat": {"bold": True},
                                "horizontalAlignment": "CENTER"
                            }
                        },
                        "fields": "userEnteredFormat(backgroundColor,textFormat,horizontalAlignment)"
                    }
                },
                {
                    "setBasicFilter": {
                        "filter": {
                            "range": {
                                "sheetId": sheet_id,
                                "startRowIndex": 0,
                                "endRowIndex": 1,
                                "startColumnIndex": 0,
                                "endColumnIndex": 7
                            }
                        }
                    }
                }
            ]
            self.sheets_service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_id,
                body={"requests": requests}
            ).execute()
            print(f"🎨 Header formatting applied to sheet: {sheet_name}")
        except Exception as error:
            print(f"❌ Error formatting headers: {error}")

    def write_analysis_to_sheet(self, spreadsheet_id: str, analysis_text: str, sheet_name: str = "Analysis_Summary") -> bool:
        try:
            self.create_summary_sheet(spreadsheet_id, sheet_name)
            parsed_data = self.parse_pipe_delimited_response(analysis_text)
            self.sheets_service.spreadsheets().values().clear(
                spreadsheetId=spreadsheet_id,
                range=f"{sheet_name}!A:Z"
            ).execute()
            self.sheets_service.spreadsheets().values().update(
                spreadsheetId=spreadsheet_id,
                range=f"{sheet_name}!A1",
                valueInputOption='RAW',
                body={"values": parsed_data}
            ).execute()
            with open("response.txt", "w") as f:
                f.write(analysis_text)
            print(f"✅ Response written and saved. Now formatting headers...")
            self.format_summary_sheet_headers(spreadsheet_id, sheet_name)
            return True
        except Exception as error:
            print(f"❌ Error writing analysis to sheet: {error}")
            return False

    def parse_pipe_delimited_response(self, response_text: str) -> List[List[str]]:
        lines = response_text.strip().split('\n')
        parsed_data = [["Resource Type", "Category", "Description", "Current State or Count", "Assessment", "Recommendation (if needed)", "Priority"]]
        for line in lines:
            if '|' in line:
                row = [c.strip() for c in line.split('|')]
                row += [''] * (7 - len(row))
                parsed_data.append(row[:7])
        return parsed_data

def format_data_for_analysis(data: Dict[str, Any]) -> str:
    lines = [
        f"Spreadsheet: {data['spreadsheet_title']}",
        f"Total Rows: {data['total_rows']}",
        f"Sheet Count: {data['sheet_count']}",
        f"Ranges: {', '.join(data['ranges'])}",
        ""
    ]
    for range_name, values in data['data'].items():
        lines.append(f"Data from {range_name}:")
        for i, row in enumerate(values):
            row_str = [str(cell) if cell is not None else '' for cell in row]
            lines.append(f"Row {i+1}: {', '.join(row_str)}")
        lines.append("")
    return '\n'.join(lines)

def query_data_with_gemini(client, data_text: str) -> str:
    """Query the spreadsheet data using Gemini AI via Vertex AI"""
    prompt = get_prompt(data_text)
    try:
        response = client.models.generate_content(model="gemini-2.5-pro", contents=prompt)
        return response.text
    except Exception as error:
        raise Exception(f"Gemini API error: {error}")

def main():
    SPREADSHEET_ID = "1WrHxL8RwJFmpZ6KOSkNANqUX8CcOhbkYOJxp2sy3vlA"
    SUMMARY_SHEET_NAME = "Gemini_Analysis_Summary"
    print("🚀 MCP-Style Gemini-Google Sheets Integration\n" + "=" * 50)
    try:
        integration = MCPGeminiSheetsIntegration()
        print("\n📊 Reading data from spreadsheet...")
        data = integration.gsheets_read(spreadsheet_id=SPREADSHEET_ID)
        print(f"✅ Loaded: {data['spreadsheet_title']} | 📋 Rows: {data['total_rows']} | 📄 Sheets: {data['sheet_count']}")
        data_text = format_data_for_analysis(data)
        print("\n🤖 Running Gemini analysis...")
        response = query_data_with_gemini(integration.gemini_client, data_text)
        print("📝 Writing analysis to spreadsheet...")
        success = integration.write_analysis_to_sheet(spreadsheet_id=SPREADSHEET_ID, analysis_text=response, sheet_name=SUMMARY_SHEET_NAME)
        if success:
            print(f"✅ Analysis successfully written to '{SUMMARY_SHEET_NAME}'")
        else:
            print("❌ Failed to write analysis to sheet")
        print("\n🎉 Integration complete!")
    except Exception as error:
        print(f"❌ Error: {error}")

if __name__ == "__main__":
    main()
